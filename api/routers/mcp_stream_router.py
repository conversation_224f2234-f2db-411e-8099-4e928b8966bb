"""
MCP Streamable HTTP router for FastAPI using transport-based implementation.

This module provides Streamable HTTP endpoints for the Model Context Protocol (MCP).
It supports both general tool listing and tag-filtered tool listing with concurrent-safe design.

Features:
- Concurrent-safe design with per-connection handlers
- Automatic pagination for large tool sets
- Tag-based tool filtering
- Comprehensive error handling and logging
- Streamable HTTP transport for better performance
"""

import asyncio
import logging
import uuid
from contextvars import Context<PERSON>ar
from typing import Dict, Any, List

import mcp.types as types
from fastapi import APIRouter, Request, Depends
from mcp.server.lowlevel import Server
from mcp.server.streamable_http import StreamableHTTPServerTransport
from sqlalchemy.ext.asyncio import AsyncSession

from api.database import get_db
from api.services.mcp_service import MCPService

# Create logger
logger = logging.getLogger(__name__)

# Create FastAPI router
router = APIRouter(tags=["mcp-stream"])

# Initialize MCP server
mcp_stream_server = Server("Easy MCP Streamable HTTP Server")

_db_ctx = ContextVar("db")
_tag_ctx = ContextVar("tag", default=None)


@mcp_stream_server.list_tools()
async def list_tools() -> List[types.Tool]:
    """List available tools for this connection."""
    # Access context variables
    db = _db_ctx.get()
    tag = _tag_ctx.get(None)
    service = MCPService(db)
    return await service.list_tools(tag)


@mcp_stream_server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Handle tool execution for this connection."""
    db = _db_ctx.get()
    service = MCPService(db)
    return await service.execute_tool(name, arguments)


async def _handle_stream_connection(request: Request):
    """
    Handle Streamable HTTP connection with the given MCP server.

    This is a helper function to reduce code duplication between
    the general and tag-filtered stream endpoints.

    Args:
        request: FastAPI request object
    """
    # Create transport instance for this connection with unique session ID
    session_id = str(uuid.uuid4())
    transport = StreamableHTTPServerTransport(mcp_session_id=session_id)

    try:
        # Establish connection
        async with transport.connect() as streams:
            # Start MCP server in background
            server_task = asyncio.create_task(
                mcp_stream_server.run(
                    streams[0],  # read stream
                    streams[1],  # write stream
                    mcp_stream_server.create_initialization_options(),
                )
            )

            # Wait for server initialization
            await asyncio.sleep(0.1)

            try:
                # Handle HTTP request
                await transport.handle_request(
                    request.scope,
                    request.receive,
                    request._send
                )
            finally:
                # Clean up server task
                if not server_task.done():
                    server_task.cancel()
                    try:
                        await server_task
                    except asyncio.CancelledError:
                        pass
    except Exception as e:
        logger.error(f"Error handling stream connection: {e}")
        raise


# FastAPI endpoints
@router.post("/stream")
async def handle_stream_endpoint(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle Streamable HTTP connection for MCP without tag filtering.

    This endpoint provides access to all enabled tools in the system.
    Each connection gets its own handler and server instance for concurrent safety.

    Args:
        request: FastAPI request object
        db: Database session dependency
    """
    _db_ctx.set(db)
    _tag_ctx.set(None)

    # Handle stream connection
    await _handle_stream_connection(request)


@router.post("/stream-{tag}")
async def handle_stream_endpoint_with_tag(
    tag: str,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle Streamable HTTP connection for MCP with tag filtering.

    This endpoint provides access to tools filtered by a specific tag.
    If the tag doesn't exist, an empty tool list will be returned.

    Args:
        tag: Tag name to filter tools by
        request: FastAPI request object
        db: Database session dependency
    """
    _db_ctx.set(db)
    _tag_ctx.set(tag)

    # Handle stream connection
    await _handle_stream_connection(request)